import json
import tkinter as tk
from typing import Dict, Any
import os
from pathlib import Path
from config import Config  # 导入Config类
import tkinter.messagebox as messagebox # 导入 messagebox
import logging # 导入 logging 模块

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ConfigManager:
    def __init__(self):
        # self.config_path = self._get_config_path() # 不再直接管理路径
        self.app_config = Config.load_config()  # 加载核心配置
        self.config = self._normalize_config(self.app_config.to_dict()) # 从核心配置加载GUI所需配置

    def _get_config_path(self) -> Path:
        """获取config.json的绝对路径"""
        # 假设config.json在项目根目录，而当前文件在gui/managers/
        return Path(os.path.dirname(os.path.abspath(__file__))).parent.parent / 'config.json'

    def _normalize_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """将嵌套格式的配置转换为扁平格式，用于GUI"""
        normalized = {}

        # 检查是否是嵌套格式
        if 'api' in config:
            # 嵌套格式：提取各个部分
            api_config = config.get('api', {})
            processing_config = config.get('processing', {})
            file_types_config = config.get('file_types', {})
            output_config = config.get('output', {})
            ui_config = config.get('ui', {})

            # API配置
            normalized['api_base'] = api_config.get('base_url', '')
            normalized['api_key'] = api_config.get('key', '')
            normalized['model_name'] = api_config.get('model', 'gpt-3.5-turbo')
            normalized['temperature'] = api_config.get('temperature', 0.7)
            normalized['max_tokens'] = api_config.get('max_tokens', 2000)

            # 处理配置
            normalized['max_chunk_size'] = processing_config.get('max_chunk_size', 2000)
            normalized['question_base_chars'] = processing_config.get('question_base_chars', 2000)
            normalized['single_choice_count'] = processing_config.get('single_choice_count', 4)
            normalized['multiple_choice_count'] = processing_config.get('multiple_choice_count', 2)
            normalized['fill_blank_count'] = processing_config.get('fill_blank_count', 2)
            normalized['short_answer_count'] = processing_config.get('short_answer_count', 1)
            normalized['true_false_count'] = processing_config.get('true_false_count', 2)
            normalized['sorting_count'] = processing_config.get('sorting_count', 0)
            normalized['disable_document_splitting'] = processing_config.get('disable_document_splitting', False)
            normalized['enable_chunk_merging'] = processing_config.get('enable_chunk_merging', True)
            normalized['use_new_splitting_logic'] = processing_config.get('use_new_splitting_logic', True)

            # 文件类型配置
            normalized['enable_pdf'] = file_types_config.get('enable_pdf', True)
            normalized['enable_docx'] = file_types_config.get('enable_docx', True)
            normalized['enable_md'] = file_types_config.get('enable_md', True)
            normalized['enable_txt'] = file_types_config.get('enable_txt', True)

            # 输出配置
            normalized['csv_path'] = output_config.get('dir', '')
            normalized['csv_name'] = output_config.get('csv_filename', 'output.csv')

            # UI配置（文档路径等）
            normalized['file_path'] = ui_config.get('file_path', '')
            normalized['recursive'] = ui_config.get('recursive', True)

        else:
            # 扁平格式：直接使用
            normalized = config.copy()

        # 确保所有必需的字段都存在
        defaults = {
            'csv_path': '',
            'csv_name': 'output.csv',
            'api_base': '',
            'api_key': '',
            'model_name': 'gpt-3.5-turbo',
            'temperature': 0.7,
            'max_tokens': 2000,
            'max_chunk_size': 2000,
            'question_base_chars': 2000,
            'single_choice_count': 4,
            'multiple_choice_count': 2,
            'fill_blank_count': 2,
            'short_answer_count': 1,
            'true_false_count': 2,
            'sorting_count': 0,
            'disable_document_splitting': False,
            'enable_chunk_merging': True,
            'use_new_splitting_logic': True,
            'enable_pdf': True,
            'enable_docx': True,
            'enable_md': True,
            'enable_txt': True,
            'file_path': '',
            'recursive': True
        }

        for key, default_value in defaults.items():
            if key not in normalized:
                normalized[key] = default_value

        return normalized

    def load_config(self) -> Dict[str, Any]:
        """从Config实例加载配置"""
        try:
            # 重新加载 Config 实例以反映最新的文件状态
            self.app_config = Config.load_config() 
            logging.info("配置已成功加载。") # 添加日志
            return self._normalize_config(self.app_config.to_dict())
        except Exception as e:
            logging.exception(f"加载配置文件失败: {e}") # 添加日志
            messagebox.showerror("加载配置失败", f"加载配置文件失败，已加载默认配置: {str(e)}")
            # 如果加载失败，返回基于当前 app_config 的默认 GUI 配置
            return self._normalize_config(self.app_config.to_dict())

    def _convert_to_nested_format(self, flat_config: Dict[str, Any]) -> Dict[str, Any]:
        """将扁平格式的配置转换为嵌套格式"""
        nested_config = {
            'api': {
                'base_url': flat_config.get('api_base', ''),
                'key': flat_config.get('api_key', ''),
                'model': flat_config.get('model_name', 'gpt-3.5-turbo'),
                'max_tokens': flat_config.get('max_tokens', 2000),
                'temperature': flat_config.get('temperature', 0.7),
                'timeout': self.app_config.REQUEST_TIMEOUT,  # 从Config获取默认值
                'max_retries': self.app_config.MAX_RETRIES  # 从Config获取默认值
            },
            'processing': {
                'max_chunk_size': flat_config.get('max_chunk_size', 2000),
                'question_base_chars': flat_config.get('question_base_chars', 2000),
                'single_choice_count': flat_config.get('single_choice_count', 4),
                'multiple_choice_count': flat_config.get('multiple_choice_count', 2),
                'fill_blank_count': flat_config.get('fill_blank_count', 2),
                'short_answer_count': flat_config.get('short_answer_count', 1),
                'true_false_count': flat_config.get('true_false_count', 2),
                'sorting_count': flat_config.get('sorting_count', 0),
                'disable_document_splitting': flat_config.get('disable_document_splitting', False),
                'enable_chunk_merging': flat_config.get('enable_chunk_merging', True),
                'use_new_splitting_logic': flat_config.get('use_new_splitting_logic', True)
            },
            'file_types': {
                'enable_pdf': flat_config.get('enable_pdf', True), # 确保这里有enable_pdf
                'enable_docx': flat_config.get('enable_docx', True),
                'enable_md': flat_config.get('enable_md', True),
                'enable_txt': flat_config.get('enable_txt', True)
            },
            'filtering': {
                'enable_question_filtering': self.app_config.ENABLE_QUESTION_FILTERING,  # 从Config获取默认值
                'filter_keywords': self.app_config.FILTER_KEYWORDS  # 从Config获取默认值
            },
            'output': {
                'dir': flat_config.get('csv_path', ''),
                'error_dir': self.app_config.ERROR_DIR,
                'csv_filename': flat_config.get('csv_name', 'output.csv'),
                'save_interval': self.app_config.PROGRESS_SAVE_INTERVAL,
                'csv_batch_save_count': self.app_config.CSV_BATCH_SAVE_COUNT,
            },
            'ui': {
                'file_path': flat_config.get('file_path', ''),
                'recursive': flat_config.get('recursive', True)
            }
        }
        return nested_config

    def load_config_to_gui(self, gui_vars: Dict[str, tk.Variable]) -> None:
        """将配置加载到GUI控件中"""
        # 加载CSV配置
        gui_vars['csv_path_var'].set(self.app_config.OUTPUT_DIR)
        gui_vars['csv_name_var'].set(self.app_config.CSV_FILENAME)

        # 加载LLM配置
        gui_vars['api_base_var'].set(self.app_config.API_BASE_URL)
        gui_vars['api_key_var'].set(self.app_config.API_KEY)
        gui_vars['model_name_var'].set(self.app_config.MODEL_NAME)
        gui_vars['temperature_var'].set(self.app_config.TEMPERATURE)
        gui_vars['max_tokens_var'].set(self.app_config.MAX_TOKENS)

        # 加载文本处理配置
        gui_vars['max_chunk_size_var'].set(self.app_config.MAX_CHUNK_SIZE)
        gui_vars['question_base_chars_var'].set(self.app_config.BASE_CHAR_THRESHOLD)

        # 加载题型数量配置
        gui_vars['single_choice_var'].set(self.app_config.SINGLE_CHOICE_COUNT)
        gui_vars['multiple_choice_var'].set(self.app_config.MULTIPLE_CHOICE_COUNT)
        gui_vars['fill_blank_var'].set(self.app_config.FILL_BLANK_COUNT)
        gui_vars['short_answer_var'].set(self.app_config.SHORT_ANSWER_COUNT)
        gui_vars['true_false_var'].set(self.app_config.TRUE_FALSE_COUNT)
        gui_vars['sorting_var'].set(self.app_config.SORTING_COUNT)

        # 加载文件类型选择配置
        gui_vars['enable_pdf_var'].set(self.app_config.ENABLE_PDF)
        gui_vars['enable_docx_var'].set(self.app_config.ENABLE_DOCX)
        gui_vars['enable_md_var'].set(self.app_config.ENABLE_MD)
        gui_vars['enable_txt_var'].set(self.app_config.ENABLE_TXT)

        # 加载文档分割配置
        gui_vars['disable_splitting_var'].set(self.app_config.DISABLE_DOCUMENT_SPLITTING)
        gui_vars['use_new_splitting_var'].set(self.app_config.USE_NEW_SPLITTING_LOGIC)

        # 加载文档选择配置
        if 'file_path_var' in gui_vars:
            gui_vars['file_path_var'].set(self.app_config.FILE_PATH)
        if 'recursive_var' in gui_vars:
            gui_vars['recursive_var'].set(self.app_config.RECURSIVE)

    def save_config_and_notify(self, gui_vars: Dict[str, tk.Variable], status_var: tk.StringVar) -> None:
        """保存配置并通知状态"""
        try:
            logging.info(f"save_config_and_notify 接收到的 gui_vars: {list(gui_vars.keys())}") # 新增日志
            # 从 GUI 变量收集配置数据
            gui_config_data = {
                "api": {
                    "base_url": gui_vars['api_base_var'].get(),
                    "key": gui_vars['api_key_var'].get(),
                    "model": gui_vars['model_name_var'].get(),
                    "max_tokens": int(gui_vars['max_tokens_var'].get()),
                    "temperature": float(gui_vars['temperature_var'].get()),
                },
                "processing": {
                    "max_chunk_size": int(gui_vars['max_chunk_size_var'].get()),
                    "question_base_chars": int(gui_vars['question_base_chars_var'].get()),
                    "disable_document_splitting": gui_vars['disable_splitting_var'].get(),
                    "enable_chunk_merging": gui_vars['enable_chunk_merging_var'].get(),
                    "use_new_splitting_logic": gui_vars['use_new_splitting_var'].get(),
                    "single_choice_count": int(gui_vars['single_choice_var'].get()),
                    "multiple_choice_count": int(gui_vars['multiple_choice_var'].get()),
                    "fill_blank_count": int(gui_vars['fill_blank_var'].get()),
                    "short_answer_count": int(gui_vars['short_answer_var'].get()),
                    "true_false_count": int(gui_vars['true_false_var'].get()),
                    "sorting_count": int(gui_vars['sorting_var'].get()),
                },
                "file_types": {
                    "enable_pdf": gui_vars['enable_pdf_var'].get(),
                    "enable_docx": gui_vars['enable_docx_var'].get(),
                    "enable_md": gui_vars['enable_md_var'].get(),
                    "enable_txt": gui_vars['enable_txt_var'].get(),
                },
                "output": {
                    "dir": gui_vars['csv_path_var'].get(),
                    "error_dir": self.app_config.ERROR_DIR,
                    "csv_filename": gui_vars['csv_name_var'].get(),
                    "save_interval": self.app_config.PROGRESS_SAVE_INTERVAL,
                    "csv_batch_save_count": self.app_config.CSV_BATCH_SAVE_COUNT,
                },
                "ui": {
                    "file_path": gui_vars['file_path_var'].get(),
                    "recursive": gui_vars['recursive_var'].get(),
                }
            }

            # 使用 Config 实例的 update_from_dict 方法更新配置
            self.app_config.update_from_dict(gui_config_data)

            logging.info(f"在保存前，app_config.API_KEY 的值为: {self.app_config.API_KEY}")
            logging.info(f"在调用 save_to_json_file 前，app_config 实例的 __dict__ 内容: {self.app_config.__dict__}")
            self.app_config.save_to_json_file()
            # 更新内部配置缓存 (不再需要，因为直接操作app_config)
            self.config = self._normalize_config(self.app_config.to_dict())
            status_var.set("设置已保存")
            logging.info("配置已成功保存。")
        except Exception as e:
            status_var.set(f"保存设置失败: {str(e)}")
            messagebox.showerror("保存失败", f"保存设置失败: {str(e)}")
            logging.exception(f"保存配置文件失败: {e}")