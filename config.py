# 配置文件 - 文档到题库工具
import os
import json
import configparser
from typing import Dict, Any, List
from pathlib import Path
import logging

class Config:
    """配置类，支持OpenAI兼容的API接口"""

    # OpenAI兼容API配置
    API_BASE_URL = "https://api.openai.com/v1"  # 可自定义API端点
    API_KEY = ""  # API密钥，可通过环境变量或命令行参数设置
    ENCODING_NAME = "cl100k_base" # tiktoken编码名称，例如 "cl100k_base"
    MODEL_NAME = "gpt-3.5-turbo"  # 模型名称，可自定义

    @classmethod
    def create_default_config_if_missing(cls):
        """如果没有配置文件，创建默认配置文件"""
        config_files = ['config.json', 'config.ini']

        # 检查是否存在任何配置文件
        if not any(os.path.exists(f) for f in config_files):
            print("未找到配置文件，创建默认配置...")

            # 创建默认配置
            default_config = cls()

            # 创建config.json示例
            try:
                default_config.save_to_json_file("config.json")
                print("✅ 已创建默认配置文件: config.json")
                print("请编辑此文件设置您的API密钥")
                return True
            except Exception as e:
                print(f"❌ 创建配置文件失败: {str(e)}")
                return False

        return True

    # API请求配置
    MAX_TOKENS = 3333  # 生成回答的最大token数
    TEMPERATURE = 0.3  # 生成温度，控制随机性
    REQUEST_TIMEOUT = 60  # 请求超时时间（秒）
    MAX_RETRIES = 3  # 最大重试次数
    RETRY_DELAY = 2  # 重试间隔（秒）
    API_RATE_LIMIT = 1.0  # API请求速率限制（每秒请求次数）

    # 文档处理配置
    MAX_CHUNK_SIZE = 2000  # 每个分块的最大字符数
    DISABLE_DOCUMENT_SPLITTING = False  # 是否禁用文档分割，True时对整个文档生成题目
    ENABLE_CHUNK_MERGING = True # 是否启用分块合并，True时内容不足会自动合并

    # 新的分块逻辑配置
    USE_NEW_SPLITTING_LOGIC = True  # 是否使用新的分块逻辑
    BASE_CHAR_THRESHOLD = 2000  # 基础字数阈值

    # 基础题目数量配置（用于新的出题逻辑）
    BASE_SINGLE_CHOICE_COUNT = 2  # 基础单选题数量
    BASE_MULTIPLE_CHOICE_COUNT = 1  # 基础多选题数量
    BASE_FILL_BLANK_COUNT = 1  # 基础填空题数量
    BASE_SHORT_ANSWER_COUNT = 1  # 基础问答题数量
    BASE_TRUE_FALSE_COUNT = 1  # 基础判断题数量
    BASE_SORTING_COUNT = 0  # 基础排序题数量

    # 题型数量配置 - 每个文本块生成的各类题目数量
    SINGLE_CHOICE_COUNT = 2  # 单选题数量
    MULTIPLE_CHOICE_COUNT = 1  # 多选题数量
    FILL_BLANK_COUNT = 1  # 填空题数量
    SHORT_ANSWER_COUNT = 1  # 问答题数量
    TRUE_FALSE_COUNT = 1  # 判断题数量
    SORTING_COUNT = 1  # 排序题数量

    # 支持的文档格式
    SUPPORTED_FORMATS = ['.docx', '.md', '.txt', '.pdf']

    # 文件类型选择配置 - 控制哪些文件类型被处理
    ENABLE_PDF = True
    ENABLE_DOCX = True
    ENABLE_MD = True
    ENABLE_TXT = True

    # 题目过滤配置
    ENABLE_QUESTION_FILTERING = True  # 是否启用题目过滤
    FILTER_KEYWORDS = ["错误处理摘要"]  # 过滤关键词列表，包含这些关键词的题目将被过滤掉

    # 输出配置
    OUTPUT_DIR = "output"  # 输出目录
    ERROR_DIR = "error_docs"  # 错误文档目录
    CSV_FILENAME = "quiz_results.csv"  # 输出CSV文件名
    PROGRESS_SAVE_INTERVAL = 10  # 每处理多少个分块保存一次进度
    CSV_BATCH_SAVE_COUNT = 20  # 每生成多少道题目写入一次CSV文件
    AUTO_BACKUP_INTERVAL = 200 # 每生成多少道题目创建一次自动备份

    # UI 配置
    FILE_PATH = ""  # 最近打开的文件路径
    RECURSIVE = True  # 是否递归处理子目录

    # 题目生成提示词模板
    QUIZ_PROMPT_TEMPLATE = """
请根据以下文档内容生成题目。要求生成以下数量的题目：
- 单选题：{single_choice_count}道
- 多选题：{multiple_choice_count}道
- 填空题：{fill_blank_count}道
- 问答题：{short_answer_count}道
- 判断题：{true_false_count}道
- 排序题：{sorting_count}道

**文档来源信息：**
文件名：{source_filename}

**重要要求：**
1. **严格基于文档内容**：所有题目必须完全基于提供的文档内容，不得编造或添加文档中没有的信息
2. **内容充分性检查**：如果文档内容不足以生成指定数量的高质量题目，请减少题目数量或返回失败
3. **准确性优先**：宁可生成较少的高质量题目，也不要生成不准确或编造的题目
4. **直接引用**：题目应直接引用或基于文档中的具体内容，确保可追溯性
5. **识别具体制度名称**：仔细分析文档内容，识别其中提到的具体规章制度、法规、政策、标准、办法、条例、规定等名称
6. **使用具体制度名称**：在题目解析中，不要使用"根据文档"这样的通用表述，而要使用文档中提到的具体规章制度名称，如"根据《XX管理办法》"、"依据《XX实施细则》"、"按照《XX规定》"等

**制度名称识别指导**：
- 优先识别带有书名号《》的正式文件名称
- 识别包含"办法"、"规定"、"条例"、"细则"、"标准"、"制度"、"政策"、"法规"、"通知"、"意见"等关键词的制度名称
- 如果文档中没有明确的制度名称，可以根据文档标题或主要内容推断制度类型
- 如果实在无法确定具体制度名称，可以使用"本规定"、"该办法"、"相关制度"等表述，但要避免使用"根据文档"

题目格式要求：
1. 单选题提供4个选项（A、B、C、D），答案格式为"A"
2. 多选题提供4-6个选项（A、B、C、D、E、F），答案格式为"AB"或"ACD"等，不要出现以上全部选项
3. 填空题用括号表示空白处，如"今天是星期（），明天星期（）？"，答案用"|"分隔，如"三|四"
4. 问答题要求简洁明了，基于文档内容，总结成短句，不要出现"根据..."这样的表述
5. 判断题答案为"正确"或"错误"
6. 排序题提供选项，答案为正确的排序，如"CDBA"

**解析要求**：
- 每道题目的解析必须明确引用具体的规章制度名称
- 解析格式示例："根据《XX管理办法》第X条规定..."、"依据《XX实施细则》..."、"按照《XX标准》要求..."
- 如果是多个制度的综合内容，可以表述为"根据《XX办法》和《XX规定》..."

**失败处理**：如果文档内容不足以生成合适的题目，请返回包含"insufficient_content"字段的JSON响应。

请以JSON格式输出，格式如下：
{{
    "questions": [
        {{
            "type": "单选",
            "question": "题目内容",
            "options": "选项A|选项B|选项C|选项D",
            "answer": "A",
            "explanation": "根据《XX管理办法》第X条规定，...",
            "score": "1.5",
            "difficulty": "低"
        }},
        {{
            "type": "多选",
            "question": "题目内容",
            "options": "选项A|选项B|选项C|选项D",
            "answer": "AB",
            "explanation": "依据《XX实施细则》相关条款，...",
            "score": "2",
            "difficulty": "中"
        }},
        {{
            "type": "填空",
            "question": "题目内容（）和（）",
            "options": "",
            "answer": "答案1|答案2",
            "explanation": "按照《XX标准》要求，...",
            "score": "2",
            "difficulty": "低"
        }},
        {{
            "type": "问答",
            "question": "题目内容",
            "options": "",
            "answer": "参考答案",
            "explanation": "根据《XX规定》和《XX办法》，...",
            "score": "5",
            "difficulty": "高"
        }},
        {{
            "type": "判断",
            "question": "题目内容",
            "options": "",
            "answer": "正确",
            "explanation": "依据《XX条例》第X章内容，...",
            "score": "1",
            "difficulty": "低"
        }},
        {{
            "type": "排序",
            "question": "题目内容",
            "options": "选项A|选项B|选项C|选项D",
            "answer": "CDBA",
            "explanation": "按照《XX制度》规定的流程顺序，...",
            "score": "3",
            "difficulty": "中"
        }}
    ]
}}

文档内容：
{content}
"""

    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    @classmethod
    def from_env(cls):
        """从环境变量加载配置"""
        config = cls()
        config.API_BASE_URL = os.getenv('API_BASE_URL', config.API_BASE_URL)
        config.API_KEY = os.getenv('API_KEY', config.API_KEY)
        config.MODEL_NAME = os.getenv('MODEL_NAME', config.MODEL_NAME)
        config.MAX_TOKENS = int(os.getenv('MAX_TOKENS', config.MAX_TOKENS))
        config.TEMPERATURE = float(os.getenv('TEMPERATURE', config.TEMPERATURE))
        config.MAX_CHUNK_SIZE = int(os.getenv('MAX_CHUNK_SIZE', config.MAX_CHUNK_SIZE))
        config.DISABLE_DOCUMENT_SPLITTING = bool(os.getenv('DISABLE_DOCUMENT_SPLITTING', config.DISABLE_DOCUMENT_SPLITTING))
        config.ENABLE_CHUNK_MERGING = bool(os.getenv('ENABLE_CHUNK_MERGING', config.ENABLE_CHUNK_MERGING))
        config.OUTPUT_DIR = os.getenv('OUTPUT_DIR', config.OUTPUT_DIR)
        return config

    @classmethod
    def from_config_file(cls, config_file: str = "config.ini"):
        """从配置文件加载配置"""
        config = cls()

        if not os.path.exists(config_file):
            return config

        parser = configparser.ConfigParser()
        parser.read(config_file, encoding='utf-8')

        # API配置
        if parser.has_section('api'):
            api_section = parser['api']
            config.API_BASE_URL = api_section.get('base_url', config.API_BASE_URL)
            config.API_KEY = api_section.get('key', config.API_KEY)
            config.MODEL_NAME = api_section.get('model', config.MODEL_NAME)
            config.MAX_TOKENS = api_section.getint('max_tokens', config.MAX_TOKENS)
            config.TEMPERATURE = api_section.getfloat('temperature', config.TEMPERATURE)
            config.REQUEST_TIMEOUT = api_section.getint('timeout', config.REQUEST_TIMEOUT)
            config.MAX_RETRIES = api_section.getint('max_retries', config.MAX_RETRIES)

        # 处理配置
        if parser.has_section('processing'):
            proc_section = parser['processing']
            config.MAX_CHUNK_SIZE = proc_section.getint('max_chunk_size', config.MAX_CHUNK_SIZE)
            config.DISABLE_DOCUMENT_SPLITTING = proc_section.getboolean('disable_document_splitting', config.DISABLE_DOCUMENT_SPLITTING)
            config.ENABLE_CHUNK_MERGING = proc_section.getboolean('enable_chunk_merging', config.ENABLE_CHUNK_MERGING)

        # 过滤配置
        if parser.has_section('filtering'):
            filtering_section = parser['filtering']
            config.ENABLE_QUESTION_FILTERING = filtering_section.getboolean('enable_question_filtering', config.ENABLE_QUESTION_FILTERING)
            # 处理过滤关键词列表（INI格式中用逗号分隔）
            filter_keywords_str = filtering_section.get('filter_keywords', ','.join(config.FILTER_KEYWORDS))
            config.FILTER_KEYWORDS = [kw.strip() for kw in filter_keywords_str.split(',') if kw.strip()]

        # 输出配置
        if parser.has_section('output'):
            output_section = parser['output']
            config.OUTPUT_DIR = output_section.get('dir', config.OUTPUT_DIR)
            config.ERROR_DIR = output_section.get('error_dir', config.ERROR_DIR)
            config.CSV_FILENAME = output_section.get('csv_filename', config.CSV_FILENAME)
            config.PROGRESS_SAVE_INTERVAL = output_section.getint('save_interval', config.PROGRESS_SAVE_INTERVAL)
            config.CSV_BATCH_SAVE_COUNT = output_section.getint('csv_batch_save_count', config.CSV_BATCH_SAVE_COUNT)

        return config

    @classmethod
    def from_json_file(cls, json_file: str = "config.json"):
        """从JSON配置文件加载配置"""
        config = cls()

        if not os.path.exists(json_file):
            return config

        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 检查是否是新格式（嵌套结构）
            if 'api' in data:
                # 新格式：嵌套结构
                api_config = data['api']
                config.API_BASE_URL = api_config.get('base_url', config.API_BASE_URL)
                config.API_KEY = api_config.get('key', config.API_KEY)
                config.MODEL_NAME = api_config.get('model', config.MODEL_NAME)
                config.MAX_TOKENS = api_config.get('max_tokens', config.MAX_TOKENS)
                config.TEMPERATURE = api_config.get('temperature', config.TEMPERATURE)
                config.REQUEST_TIMEOUT = api_config.get('timeout', config.REQUEST_TIMEOUT)
                config.MAX_RETRIES = api_config.get('max_retries', config.MAX_RETRIES)

                # 处理配置
                if 'processing' in data:
                    proc_config = data['processing']
                    config.MAX_CHUNK_SIZE = proc_config.get('max_chunk_size', config.MAX_CHUNK_SIZE)
                    config.DISABLE_DOCUMENT_SPLITTING = proc_config.get('disable_document_splitting', config.DISABLE_DOCUMENT_SPLITTING)
                    config.ENABLE_CHUNK_MERGING = proc_config.get('enable_chunk_merging', config.ENABLE_CHUNK_MERGING)

                # 文件类型配置
                if 'file_types' in data:
                    file_types_config = data['file_types']
                    config.ENABLE_DOCX = file_types_config.get('enable_docx', config.ENABLE_DOCX)
                    config.ENABLE_MD = file_types_config.get('enable_md', config.ENABLE_MD)
                    config.ENABLE_TXT = file_types_config.get('enable_txt', config.ENABLE_TXT)
                    config.ENABLE_PDF = file_types_config.get('enable_pdf', config.ENABLE_PDF)

                # 过滤配置
                if 'filtering' in data:
                    filtering_config = data['filtering']
                    config.ENABLE_QUESTION_FILTERING = filtering_config.get('enable_question_filtering', config.ENABLE_QUESTION_FILTERING)
                    config.FILTER_KEYWORDS = filtering_config.get('filter_keywords', config.FILTER_KEYWORDS)

                # 输出配置
                if 'output' in data:
                    output_config = data['output']
                    config.OUTPUT_DIR = output_config.get('dir', config.OUTPUT_DIR)
                    config.ERROR_DIR = output_config.get('error_dir', config.ERROR_DIR)
                    config.CSV_FILENAME = output_config.get('csv_filename', config.CSV_FILENAME)
                    config.PROGRESS_SAVE_INTERVAL = output_config.get('save_interval', config.PROGRESS_SAVE_INTERVAL)
                    config.CSV_BATCH_SAVE_COUNT = output_config.get('csv_batch_save_count', config.CSV_BATCH_SAVE_COUNT)

                # UI 配置
                if 'ui' in data:
                    ui_config = data['ui']
                    config.FILE_PATH = ui_config.get('file_path', config.FILE_PATH)
                    config.RECURSIVE = ui_config.get('recursive', config.RECURSIVE)
            else:
                # 旧格式：扁平结构，兼容处理
                print("检测到旧格式配置文件，正在转换...")
                config.API_BASE_URL = data.get('api_base', config.API_BASE_URL)
                config.API_KEY = data.get('api_key', config.API_KEY)
                config.MODEL_NAME = data.get('model_name', config.MODEL_NAME)
                config.MAX_TOKENS = data.get('max_tokens', config.MAX_TOKENS)
                config.TEMPERATURE = data.get('temperature', config.TEMPERATURE)
                config.MAX_CHUNK_SIZE = data.get('max_chunk_size', config.MAX_CHUNK_SIZE)
                config.DISABLE_DOCUMENT_SPLITTING = data.get('disable_document_splitting', config.DISABLE_DOCUMENT_SPLITTING)
                config.ENABLE_CHUNK_MERGING = data.get('enable_chunk_merging', True) # 默认启用

                # 新的配置项
                config.USE_NEW_SPLITTING_LOGIC = data.get('use_new_splitting_logic', config.USE_NEW_SPLITTING_LOGIC)
                config.BASE_CHAR_THRESHOLD = data.get('base_char_threshold', config.BASE_CHAR_THRESHOLD)
                config.BASE_SINGLE_CHOICE_COUNT = data.get('base_single_choice_count', config.BASE_SINGLE_CHOICE_COUNT)
                config.BASE_MULTIPLE_CHOICE_COUNT = data.get('base_multiple_choice_count', config.BASE_MULTIPLE_CHOICE_COUNT)
                config.BASE_FILL_BLANK_COUNT = data.get('base_fill_blank_count', config.BASE_FILL_BLANK_COUNT)
                config.BASE_SHORT_ANSWER_COUNT = data.get('base_short_answer_count', config.BASE_SHORT_ANSWER_COUNT)
                config.BASE_TRUE_FALSE_COUNT = data.get('base_true_false_count', config.BASE_TRUE_FALSE_COUNT)
                config.BASE_SORTING_COUNT = data.get('base_sorting_count', config.BASE_SORTING_COUNT)

                # 自动保存为新格式
                try:
                    config.save_to_json_file(json_file)
                    print("✅ 配置文件已自动转换为新格式")
                except Exception as save_error:
                    print(f"⚠️ 自动转换配置文件失败: {save_error}")

        except Exception as e:
            print(f"加载JSON配置文件失败: {e}")

        return config

    @classmethod
    def update_from_args(cls, config, args):
        """从命令行参数更新配置"""
        if hasattr(args, 'api_base') and args.api_base:
            config.API_BASE_URL = args.api_base
        if hasattr(args, 'api_key') and args.api_key:
            config.API_KEY = args.api_key
        if hasattr(args, 'model') and args.model:
            config.MODEL_NAME = args.model
        if hasattr(args, 'max_chunk_size') and args.max_chunk_size:
            config.MAX_CHUNK_SIZE = args.max_chunk_size
        if hasattr(args, 'output_dir') and args.output_dir:
            config.OUTPUT_DIR = args.output_dir
        return config

    def save_to_config_file(self, config_file: str = "config.ini"):
        """保存配置到INI文件"""
        parser = configparser.ConfigParser()

        # API配置
        parser.add_section('api')
        parser.set('api', 'base_url', self.API_BASE_URL)
        parser.set('api', 'key', self.API_KEY)
        parser.set('api', 'model', self.MODEL_NAME)
        parser.set('api', 'max_tokens', str(self.MAX_TOKENS))
        parser.set('api', 'temperature', str(self.TEMPERATURE))
        parser.set('api', 'timeout', str(self.REQUEST_TIMEOUT))
        parser.set('api', 'max_retries', str(self.MAX_RETRIES))

        # 处理配置
        parser.add_section('processing')
        parser.set('processing', 'max_chunk_size', str(self.MAX_CHUNK_SIZE))
        parser.set('processing', 'disable_document_splitting', str(self.DISABLE_DOCUMENT_SPLITTING))
        parser.set('processing', 'enable_chunk_merging', str(self.ENABLE_CHUNK_MERGING))

        # 过滤配置
        parser.add_section('filtering')
        parser.set('filtering', 'enable_question_filtering', str(self.ENABLE_QUESTION_FILTERING))
        parser.set('filtering', 'filter_keywords', ','.join(self.FILTER_KEYWORDS))

        # 输出配置
        parser.add_section('output')
        parser.set('output', 'dir', self.OUTPUT_DIR)
        parser.set('output', 'error_dir', self.ERROR_DIR)
        parser.set('output', 'csv_filename', self.CSV_FILENAME)
        parser.set('output', 'save_interval', str(self.PROGRESS_SAVE_INTERVAL))
        parser.set('output', 'csv_batch_save_count', str(self.CSV_BATCH_SAVE_COUNT))

        with open(config_file, 'w', encoding='utf-8') as f:
            parser.write(f)

    def save_to_json_file(self, json_file: str = "config.json"):
        """保存配置到JSON文件"""
        config_data = self.to_dict() # 获取最新的配置数据
        logging.info(f"Config 实例的 __dict__ 内容: {self.__dict__}") # 新增日志
        logging.info(f"Config.to_dict() 内部的 API_KEY 值为: {self.API_KEY}") # 添加日志
        logging.info(f"即将保存到config.json的数据 (来自 to_dict()): {json.dumps(config_data, indent=2, ensure_ascii=False)}") # 添加日志

        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)

    @classmethod
    def load_config(cls, config_file: str = None):
        """智能加载配置，优先级：命令行参数 > 配置文件 > 环境变量 > 默认值"""
        # 1. 从默认值开始
        config = cls()

        # 2. 加载环境变量
        env_config = cls.from_env()
        for attr in dir(env_config):
            if not attr.startswith('_') and hasattr(config, attr):
                env_value = getattr(env_config, attr)
                if env_value != getattr(cls(), attr):  # 如果不是默认值
                    setattr(config, attr, env_value)

        # 3. 加载配置文件
        if config_file:
            if config_file.endswith('.json'):
                file_config = cls.from_json_file(config_file)
            else:
                file_config = cls.from_config_file(config_file)

            # 合并配置
            for attr in dir(file_config):
                if not attr.startswith('_') and hasattr(config, attr):
                    file_value = getattr(file_config, attr)
                    if file_value != getattr(cls(), attr):  # 如果不是默认值
                        setattr(config, attr, file_value)
        else:
            # 尝试自动查找配置文件
            for filename in ['config.json', 'config.ini']:
                if os.path.exists(filename):
                    if filename.endswith('.json'):
                        file_config = cls.from_json_file(filename)
                    else:
                        file_config = cls.from_config_file(filename)

                    # 合并配置
                    for attr in dir(file_config):
                        if not attr.startswith('_') and hasattr(config, attr):
                            file_value = getattr(file_config, attr)
                            if file_value != getattr(cls(), attr):  # 如果不是默认值
                                setattr(config, attr, file_value)
                    break

        return config

    def is_api_configured(self) -> bool:
        """检查API是否已配置"""
        return bool(self.API_KEY and self.API_KEY != "")

    def get_missing_config_items(self) -> List[str]:
        """获取缺失的配置项"""
        missing = []
        if not self.API_KEY or self.API_KEY == "":
            missing.append("API密钥")
        if not self.API_BASE_URL:
            missing.append("API基础URL")
        if not self.MODEL_NAME:
            missing.append("模型名称")
        return missing

    def get_enabled_formats(self) -> List[str]:
        """根据配置返回启用的文档格式列表"""
        enabled_formats = []
        if self.ENABLE_DOCX:
            enabled_formats.append('.docx')
        if self.ENABLE_MD:
            enabled_formats.append('.md')
        if self.ENABLE_TXT:
            enabled_formats.append('.txt')
        if self.ENABLE_PDF:
            enabled_formats.append('.pdf')
        return enabled_formats

    def create_default_config_file(self, format_type: str = "json") -> str:
        """创建默认配置文件"""
        if format_type.lower() == "json":
            filename = "config.json"
            self.save_to_json_file(filename)
        else:
            filename = "config.ini"
            self.save_to_config_file(filename)
        return filename

    @classmethod
    def setup_wizard(cls):
        """配置设置向导"""
        print("=" * 60)
        print("文档到题库工具 - 配置设置向导")
        print("=" * 60)

        config = cls()

        # API配置
        print("\n1. API配置")
        print("支持的API服务商:")
        print("  1) OpenAI官方 (https://api.openai.com/v1)")
        print("  2) DeepSeek (https://api.deepseek.com/v1)")
        print("  3) 智谱AI (https://open.bigmodel.cn/api/paas/v4)")
        print("  4) 月之暗面 (https://api.moonshot.cn/v1)")
        print("  5) 自定义")

        choice = input("\n请选择API服务商 (1-5): ").strip()

        if choice == "1":
            config.API_BASE_URL = "https://api.openai.com/v1"
            config.MODEL_NAME = "gpt-3.5-turbo"
        elif choice == "2":
            config.API_BASE_URL = "https://api.deepseek.com/v1"
            config.MODEL_NAME = "deepseek-chat"
        elif choice == "3":
            config.API_BASE_URL = "https://open.bigmodel.cn/api/paas/v4"
            config.MODEL_NAME = "glm-4"
        elif choice == "4":
            config.API_BASE_URL = "https://api.moonshot.cn/v1"
            config.MODEL_NAME = "moonshot-v1-8k"
        elif choice == "5":
            config.API_BASE_URL = input("请输入API基础URL: ").strip()
            config.MODEL_NAME = input("请输入模型名称: ").strip()

        config.API_KEY = input("请输入API密钥: ").strip()

        # 处理配置
        print("\n2. 处理配置")
        chunk_size = input(f"文本块大小 (默认 {config.MAX_CHUNK_SIZE}): ").strip()
        if chunk_size:
            config.MAX_CHUNK_SIZE = int(chunk_size)

        # 保存配置
        print("\n3. 保存配置")
        format_choice = input("配置文件格式 (json/ini, 默认json): ").strip().lower()
        if not format_choice:
            format_choice = "json"

        filename = config.create_default_config_file(format_choice)
        print(f"\n✅ 配置已保存到: {filename}")
        print("现在可以使用工具了！")

        return config

    def to_dict(self):
        """将当前配置转换为字典格式"""
        return {
            "api": {
                "base_url": self.API_BASE_URL,
                "key": self.API_KEY,
                "model": self.MODEL_NAME,
                "max_tokens": self.MAX_TOKENS,
                "temperature": self.TEMPERATURE,
                "timeout": self.REQUEST_TIMEOUT,
                "max_retries": self.MAX_RETRIES,
                "retry_delay": self.RETRY_DELAY,
                "api_rate_limit": self.API_RATE_LIMIT
            },
            "processing": {
                "max_chunk_size": self.MAX_CHUNK_SIZE,
                "disable_document_splitting": self.DISABLE_DOCUMENT_SPLITTING,
                "enable_chunk_merging": self.ENABLE_CHUNK_MERGING,
                "use_new_splitting_logic": self.USE_NEW_SPLITTING_LOGIC,
                "base_char_threshold": self.BASE_CHAR_THRESHOLD,
                "base_single_choice_count": self.BASE_SINGLE_CHOICE_COUNT,
                "base_multiple_choice_count": self.BASE_MULTIPLE_CHOICE_COUNT,
                "base_fill_blank_count": self.BASE_FILL_BLANK_COUNT,
                "base_short_answer_count": self.BASE_SHORT_ANSWER_COUNT,
                "base_true_false_count": self.BASE_TRUE_FALSE_COUNT,
                "base_sorting_count": self.BASE_SORTING_COUNT,
                "single_choice_count": self.SINGLE_CHOICE_COUNT,
                "multiple_choice_count": self.MULTIPLE_CHOICE_COUNT,
                "fill_blank_count": self.FILL_BLANK_COUNT,
                "short_answer_count": self.SHORT_ANSWER_COUNT,
                "true_false_count": self.TRUE_FALSE_COUNT,
                "sorting_count": self.SORTING_COUNT
            },
            "file_types": {
                "enable_docx": self.ENABLE_DOCX,
                "enable_md": self.ENABLE_MD,
                "enable_txt": self.ENABLE_TXT,
                "enable_pdf": self.ENABLE_PDF
            },
            "filtering": {
                "enable_question_filtering": self.ENABLE_QUESTION_FILTERING,
                "filter_keywords": self.FILTER_KEYWORDS
            },
            "output": {
                "dir": self.OUTPUT_DIR,
                "error_dir": self.ERROR_DIR,
                "csv_filename": self.CSV_FILENAME,
                "save_interval": self.PROGRESS_SAVE_INTERVAL,
                "csv_batch_save_count": self.CSV_BATCH_SAVE_COUNT,
                "auto_backup_interval": self.AUTO_BACKUP_INTERVAL
            },
            "ui": {
                "file_path": self.FILE_PATH,
                "recursive": self.RECURSIVE
            }
        }

    def update_from_dict(self, data: Dict[str, Any]):
        """从字典更新配置"""
        if 'api' in data:
            api_data = data['api']
            self.API_BASE_URL = api_data.get('base_url', self.API_BASE_URL)
            self.API_KEY = api_data.get('key', self.API_KEY)
            self.MODEL_NAME = api_data.get('model', self.MODEL_NAME)
            self.MAX_TOKENS = api_data.get('max_tokens', self.MAX_TOKENS)
            self.TEMPERATURE = api_data.get('temperature', self.TEMPERATURE)
            self.REQUEST_TIMEOUT = api_data.get('timeout', self.REQUEST_TIMEOUT)
            self.MAX_RETRIES = api_data.get('max_retries', self.MAX_RETRIES)
            self.RETRY_DELAY = api_data.get('retry_delay', self.RETRY_DELAY)
            self.API_RATE_LIMIT = api_data.get('api_rate_limit', self.API_RATE_LIMIT)

        if 'processing' in data:
            proc_data = data['processing']
            self.MAX_CHUNK_SIZE = proc_data.get('max_chunk_size', self.MAX_CHUNK_SIZE)
            self.DISABLE_DOCUMENT_SPLITTING = proc_data.get('disable_document_splitting', self.DISABLE_DOCUMENT_SPLITTING)
            self.ENABLE_CHUNK_MERGING = proc_data.get('enable_chunk_merging', self.ENABLE_CHUNK_MERGING)
            self.USE_NEW_SPLITTING_LOGIC = proc_data.get('use_new_splitting_logic', self.USE_NEW_SPLITTING_LOGIC)
            self.BASE_CHAR_THRESHOLD = proc_data.get('base_char_threshold', self.BASE_CHAR_THRESHOLD)
            self.BASE_SINGLE_CHOICE_COUNT = proc_data.get('base_single_choice_count', self.BASE_SINGLE_CHOICE_COUNT)
            self.BASE_MULTIPLE_CHOICE_COUNT = proc_data.get('base_multiple_choice_count', self.BASE_MULTIPLE_CHOICE_COUNT)
            self.BASE_FILL_BLANK_COUNT = proc_data.get('base_fill_blank_count', self.BASE_FILL_BLANK_COUNT)
            self.BASE_SHORT_ANSWER_COUNT = proc_data.get('base_short_answer_count', self.BASE_SHORT_ANSWER_COUNT)
            self.BASE_TRUE_FALSE_COUNT = proc_data.get('base_true_false_count', self.BASE_TRUE_FALSE_COUNT)
            self.BASE_SORTING_COUNT = proc_data.get('base_sorting_count', self.BASE_SORTING_COUNT)
            self.SINGLE_CHOICE_COUNT = proc_data.get('single_choice_count', self.SINGLE_CHOICE_COUNT)
            self.MULTIPLE_CHOICE_COUNT = proc_data.get('multiple_choice_count', self.MULTIPLE_CHOICE_COUNT)
            self.FILL_BLANK_COUNT = proc_data.get('fill_blank_count', self.FILL_BLANK_COUNT)
            self.SHORT_ANSWER_COUNT = proc_data.get('short_answer_count', self.SHORT_ANSWER_COUNT)
            self.TRUE_FALSE_COUNT = proc_data.get('true_false_count', self.TRUE_FALSE_COUNT)
            self.SORTING_COUNT = proc_data.get('sorting_count', self.SORTING_COUNT)

        if 'file_types' in data:
            file_types_data = data['file_types']
            self.ENABLE_DOCX = file_types_data.get('enable_docx', self.ENABLE_DOCX)
            self.ENABLE_MD = file_types_data.get('enable_md', self.ENABLE_MD)
            self.ENABLE_TXT = file_types_data.get('enable_txt', self.ENABLE_TXT)
            self.ENABLE_PDF = file_types_data.get('enable_pdf', self.ENABLE_PDF)

        if 'filtering' in data:
            filtering_data = data['filtering']
            self.ENABLE_QUESTION_FILTERING = filtering_data.get('enable_question_filtering', self.ENABLE_QUESTION_FILTERING)
            self.FILTER_KEYWORDS = filtering_data.get('filter_keywords', self.FILTER_KEYWORDS)

        if 'output' in data:
            output_data = data['output']
            self.OUTPUT_DIR = output_data.get('dir', self.OUTPUT_DIR)
            self.ERROR_DIR = output_data.get('error_dir', self.ERROR_DIR)
            self.CSV_FILENAME = output_data.get('csv_filename', self.CSV_FILENAME)
            self.PROGRESS_SAVE_INTERVAL = output_data.get('save_interval', self.PROGRESS_SAVE_INTERVAL)
            self.CSV_BATCH_SAVE_COUNT = output_data.get('csv_batch_save_count', self.CSV_BATCH_SAVE_COUNT)
            self.AUTO_BACKUP_INTERVAL = output_data.get('auto_backup_interval', self.AUTO_BACKUP_INTERVAL)

        if 'ui' in data:
            ui_data = data['ui']
            self.FILE_PATH = ui_data.get('file_path', self.FILE_PATH)
            self.RECURSIVE = ui_data.get('recursive', self.RECURSIVE)
