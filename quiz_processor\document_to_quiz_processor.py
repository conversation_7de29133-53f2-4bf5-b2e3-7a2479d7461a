import os
import sys
import logging
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from document_processor.doc_reader import DocumentReader
from document_processor.text_splitter import TextSplitter
from llm_service.openai_client import OpenAICompatibleClient
from output_manager.csv_writer import CSVWriter
from output_manager.incremental_saver import IncrementalSaver
from output_manager.progress_saver import ProgressSaver
from error_handler.error_manager import ErrorManager
from utils.helpers import (
    setup_logging, validate_input_directory, create_argument_parser,
    print_banner, print_progress_bar, validate_api_config,
    estimate_processing_time, summarize_results, scan_directory_structure
)
from utils.question_calculator import QuestionCalculator

logger = logging.getLogger(__name__)

class DocumentToQuizProcessor:
    """文档到题库处理器主类"""

    def __init__(self, config: Config, question_queue: Any = None):
        """
        初始化处理器

        Args:
            config: 配置对象
            question_queue: 消息队列，用于与GUI通信（可选）
        """
        self.config = config
        self.question_queue = question_queue

        # 初始化各个组件
        # 根据配置获取启用的文件格式
        enabled_formats = config.get_enabled_formats()
        self.doc_reader = DocumentReader(enabled_formats=enabled_formats)
        self.text_splitter = TextSplitter(
            max_chunk_size=config.MAX_CHUNK_SIZE,
            disable_splitting=config.DISABLE_DOCUMENT_SPLITTING,
            enable_chunk_merging=config.ENABLE_CHUNK_MERGING,
            use_new_splitting_logic=getattr(config, 'USE_NEW_SPLITTING_LOGIC', False)
        )
        self.llm_client = OpenAICompatibleClient(config)
        self.csv_writer = CSVWriter(config)
        self.incremental_saver = IncrementalSaver(config, self.csv_writer, self.question_queue)
        self.progress_saver = ProgressSaver(config)
        self.error_manager = ErrorManager(config)

        # 初始化题目数量计算器
        self.question_calculator = QuestionCalculator(config)

        logger.info("文档到题库处理器初始化完成")

    def process_documents(self, input_dir: str, resume: bool = False, recursive: bool = True) -> Dict[str, Any]:
        """
        处理文档目录

        Args:
            input_dir: 输入目录
            resume: 是否从上次中断处继续
            recursive: 是否递归处理子文件夹，默认为True

        Returns:
            处理结果摘要
        """
        logger.info(f"开始处理文档目录: {input_dir} (递归: {recursive})")

        # 先扫描目录结构，显示统计信息
        enabled_formats = self.config.get_enabled_formats()
        structure_info = scan_directory_structure(input_dir, recursive, enabled_formats)
        if 'error' not in structure_info:
            logger.info(f"目录扫描结果: 总文件 {structure_info['total_files']} 个，支持的文档 {structure_info['supported_files']} 个")
            logger.info(f"文件类型分布: {structure_info['file_types']}")

        # 检查是否需要恢复进度
        if resume:
            progress_data = self.progress_saver.load_progress()
            if progress_data:
                return self._resume_processing(input_dir, progress_data, recursive)

        # 全新开始处理
        return self._start_fresh_processing(input_dir, recursive)

    def _start_fresh_processing(self, input_dir: str, recursive: bool = True) -> Dict[str, Any]:
        """开始全新的处理流程"""
        # 1. 读取文档
        logger.info("步骤 1/4: 读取文档...")
        documents = self.doc_reader.batch_read_documents(input_dir, recursive)

        if not documents:
            logger.error("没有找到可处理的文档")
            return {'error': '没有找到可处理的文档'}

        logger.info(f"成功读取 {len(documents)} 个文档")

        # 2. 分割文本
        logger.info("步骤 2/4: 分割文本...")
        chunks = self.text_splitter.batch_split_documents(documents)

        if not chunks:
            logger.error("文本分割失败")
            return {'error': '文本分割失败'}

        logger.info(f"文本分割完成，共生成 {len(chunks)} 个文本块")

        # 估算处理时间
        estimated_time = estimate_processing_time(len(chunks), self.config.QUESTIONS_PER_CHUNK)
        logger.info(f"预计处理时间: {estimated_time}")

        # 3. 生成题目
        logger.info("步骤 3/4: 生成题目...")
        all_questions, failed_chunks, successful_chunk_indices = self._process_chunks(chunks)

        # 4. 保存结果
        logger.info("步骤 4/4: 保存结果...")
        result = self._save_results(all_questions, failed_chunks, successful_chunk_indices)

        # 清理进度文件
        self.progress_saver.clear_progress()

        return result

    def _resume_processing(self, input_dir: str, progress_data: Dict[str, Any], recursive: bool = True) -> Dict[str, Any]:
        """从上次中断处恢复处理"""
        logger.info("从上次中断处恢复处理...")

        # 获取已处理的数据
        processed_chunks = progress_data.get('processed_chunks', [])
        generated_questions = progress_data.get('generated_questions', [])
        failed_chunks = progress_data.get('failed_chunks', [])
        current_index = progress_data.get('current_index', 0)
        successful_chunk_indices = progress_data.get('successful_chunk_indices', [])

        logger.info(f"已处理 {len(processed_chunks)} 个文本块，生成 {len(generated_questions)} 道题目")

        # 重新读取文档并分割
        documents = self.doc_reader.batch_read_documents(input_dir, recursive)
        all_chunks = self.text_splitter.batch_split_documents(documents)

        # 处理剩余的文本块
        remaining_chunks = all_chunks[current_index:]

        if remaining_chunks:
            logger.info(f"继续处理剩余的 {len(remaining_chunks)} 个文本块")
            new_questions, new_failed, new_successful_chunk_indices = self._process_chunks(remaining_chunks, current_index)

            # 合并结果
            generated_questions.extend(new_questions)
            failed_chunks.extend(new_failed)
            successful_chunk_indices.extend(new_successful_chunk_indices)

        # 保存最终结果
        result = self._save_results(generated_questions, failed_chunks, successful_chunk_indices)

        # 清理进度文件
        self.progress_saver.clear_progress()

        return result

    def _process_chunks(self, chunks: List[Dict[str, Any]], start_index: int = 0) -> tuple:
        """处理文本块生成题目，支持增量保存和智能合并"""
        all_questions = []
        failed_chunks = []
        successful_chunk_indices = []

        total_chunks = len(chunks)
        i = 0  # 使用while循环以支持动态跳过已合并的分块

        while i < len(chunks):
            current_index = start_index + i
            chunk = chunks[i]

            try:
                # 检查chunk是否为None（防御性编程）
                if chunk is None or not isinstance(chunk, dict):
                    logger.warning(f"检测到无效的chunk，跳过处理 - 索引: {i}")
                    i += 1
                    continue

                # 显示进度
                print_progress_bar(
                    i + 1, total_chunks,
                    prefix=f"处理文本块",
                    suffix=f"({chunk.get('filename', '未知文件')})"
                )

                # 根据字符数计算题目数量
                question_counts = self.question_calculator.get_question_counts_for_chunk(chunk)

                # 生成题目
                quiz_data = self.llm_client.generate_quiz(
                    content=chunk.get('content', ''),
                    source_filename=chunk.get('filename', '未知文档'),
                    question_counts=question_counts
                )

                if quiz_data and 'questions' in quiz_data:
                    # 为每个题目添加来源信息
                    chunk_questions = []
                    for question in quiz_data['questions']:
                        question['source_file'] = chunk.get('filename', '未知文件')
                        question['chunk_index'] = chunk.get('chunk_index', 0)
                        chunk_questions.append(question)
                        all_questions.append(question)

                    # 记录成功生成题目的文本块索引
                    if quiz_data['questions']:
                        chunk_index = chunk.get('chunk_index', 0) if chunk else 0
                        successful_chunk_indices.append(chunk_index)

                    # 增量保存题目
                    save_result = self.incremental_saver.add_questions(chunk_questions)
                    if save_result.get('saved'):
                        logger.info(f"增量保存: {save_result.get('questions_saved', 0)} 道题目")
                        if save_result.get('backup_info', {}).get('created'):
                            logger.info(f"自动备份: {save_result['backup_info']['backup_path']}")

                    logger.debug(f"成功生成 {len(quiz_data['questions'])} 道题目")
                    i += 1  # 正常处理下一个分块

                elif quiz_data is None:
                    # 内容不足，检查是否禁用了分块
                    if self.config.DISABLE_DOCUMENT_SPLITTING:
                        # 如果禁用了分块，不进行合并，直接记录为失败
                        safe_filename = chunk.get('filename', '未知文件') if chunk else '未知文件'
                        logger.warning(f"文档分割已禁用，文本块内容不足以生成题目: {safe_filename}")
                        failed_chunk = {
                            **chunk,
                            'failure_reason': 'insufficient_content_no_splitting'
                        }
                        failed_chunks.append(failed_chunk)

                        # 保存失败的文本块
                        has_generated_questions = len(all_questions) > 0
                        self.error_manager.save_failed_chunk(failed_chunk, "文档分割已禁用，内容不足以生成题目", has_generated_questions=has_generated_questions)
                        i += 1
                    elif not self.config.ENABLE_CHUNK_MERGING: # 新增：如果禁用分块合并
                        safe_filename = chunk.get('filename', '未知文件') if chunk else '未知文件'
                        logger.warning(f"分块合并已禁用，文本块内容不足以生成题目: {safe_filename}")
                        failed_chunk = {
                            **chunk,
                            'failure_reason': 'insufficient_content_no_merging'
                        }
                        failed_chunks.append(failed_chunk)
                        has_generated_questions = len(all_questions) > 0
                        self.error_manager.save_failed_chunk(failed_chunk, "分块合并已禁用，内容不足以生成题目", has_generated_questions=has_generated_questions)
                        i += 1
                    else:
                        # 启用了分块，尝试合并分块
                        merged_result = self._try_merge_chunks_for_generation(chunks, i)

                        if merged_result['success']:
                            # 合并成功，处理合并后的分块
                            merged_chunk = merged_result['merged_chunk']
                            next_index = merged_result['next_index']

                            safe_merged_filename = merged_chunk.get('filename', '未知文件') if merged_chunk else '未知文件'
                            safe_merged_chunk_index = merged_chunk.get('chunk_index', 0) if merged_chunk else 0
                            safe_merged_char_count = merged_chunk.get('char_count', 0) if merged_chunk else 0

                            logger.info(f"分块合并成功，尝试生成题目: {safe_merged_filename} "
                                       f"索引 {safe_merged_chunk_index}, 字符数: {safe_merged_char_count}")

                            # 根据合并后的字符数计算题目数量
                            merged_question_counts = self.question_calculator.get_question_counts_for_chunk(merged_chunk)

                            # 尝试为合并后的分块生成题目
                            merged_quiz_data = self.llm_client.generate_quiz(
                                content=merged_chunk.get('content', '') if merged_chunk else '',
                                source_filename=merged_chunk.get('filename', '未知文档') if merged_chunk else '未知文档',
                                question_counts=merged_question_counts
                            )

                            if merged_quiz_data and 'questions' in merged_quiz_data:
                                # 为每个题目添加来源信息
                                chunk_questions = []
                                for question in merged_quiz_data['questions']:
                                    question['source_file'] = merged_chunk.get('filename', '未知文件') if merged_chunk else '未知文件'
                                    question['chunk_index'] = merged_chunk.get('chunk_index', 0) if merged_chunk else 0
                                    question['merged_from'] = merged_chunk.get('merged_from', []) if merged_chunk else []
                                    chunk_questions.append(question)
                                    all_questions.append(question)

                                # 记录成功生成题目的文本块索引
                                if merged_quiz_data['questions'] and merged_chunk:
                                    for original_idx in merged_chunk.get('merged_from', []):
                                        successful_chunk_indices.append(original_idx)
                                    # 如果是单个分块成功，也加入它的索引
                                    if not merged_chunk.get('merged_from') and 'chunk_index' in merged_chunk:
                                        successful_chunk_indices.append(merged_chunk.get('chunk_index', 0))

                                # 增量保存题目
                                save_result = self.incremental_saver.add_questions(chunk_questions)
                                if save_result.get('saved'):
                                    logger.info(f"增量保存: {save_result.get('questions_saved', 0)} 道题目")

                                logger.info(f"合并分块成功生成 {len(merged_quiz_data['questions'])} 道题目")
                            else:
                                # 合并后仍然无法生成题目，记录为失败
                                safe_merged_filename = merged_chunk.get('filename', '未知文件') if merged_chunk else '未知文件'
                                safe_merged_char_count = merged_chunk.get('char_count', 0) if merged_chunk else 0
                                logger.warning(f"处理文本块失败: {safe_merged_filename} - 原因: 合并分块后仍无法生成题目")
                                failed_chunk = {
                                    **merged_chunk,
                                    'failure_reason': 'insufficient_content_after_merge'
                                }
                                failed_chunks.append(failed_chunk)

                                # 保存失败的文本块
                                has_generated_questions = len(all_questions) > 0
                                self.error_manager.save_failed_chunk(failed_chunk, f"合并后仍无法生成题目，字符数: {safe_merged_char_count}", has_generated_questions=has_generated_questions)

                            i = next_index  # 跳到下一个未处理的分块
                        else:
                            # 无法合并或合并后仍不足，记录为失败
                            safe_filename = chunk.get('filename', '未知文件') if chunk else '未知文件'
                            logger.warning(f"处理文本块失败: {safe_filename} - 原因: 内容不足且无法有效合并")
                            failed_chunk = {
                                **chunk,
                                'failure_reason': 'insufficient_content_or_generation_failed'
                            }
                            failed_chunks.append(failed_chunk)

                            # 保存失败的文本块
                            has_generated_questions = len(all_questions) > 0
                            self.error_manager.save_failed_chunk(failed_chunk, "文本块内容不足且无法有效合并", has_generated_questions=has_generated_questions)
                            i += 1

                else:
                    safe_filename = chunk.get('filename', '未知文件') if chunk else '未知文件'
                    logger.warning(f"处理文本块失败: {safe_filename} - 原因: 响应格式错误")
                    failed_chunk = {
                        **chunk,
                        'failure_reason': 'invalid_response_format'
                    }
                    failed_chunks.append(failed_chunk)

                    # 保存失败的文本块
                    has_generated_questions = len(all_questions) > 0
                    self.error_manager.save_failed_chunk(failed_chunk, "生成题目格式错误", has_generated_questions=has_generated_questions)
                    i += 1

                # 定时保存进度
                if self.progress_saver.should_save_progress(i):
                    self.progress_saver.save_progress(
                        chunks[:i], all_questions, failed_chunks, current_index + 1
                    )

                # 显示增量保存状态
                if i % 5 == 0:  # 每5个文本块显示一次状态
                    logger.debug(f"增量保存状态: {self.incremental_saver.get_progress_info()}")

            except Exception as e:
                # 安全获取文件名用于日志记录
                safe_filename = '未知文件'
                if chunk is not None and isinstance(chunk, dict):
                    safe_filename = chunk.get('filename', '未知文件')

                logger.error(f"处理文本块失败: {safe_filename} - 原因: {str(e)}")

                # 只有当chunk不为None时才添加到失败列表
                if chunk is not None:
                    failed_chunks.append(chunk)
                    # 保存失败的文本块
                    has_generated_questions = len(all_questions) > 0
                    self.error_manager.save_failed_chunk(chunk, str(e), has_generated_questions=has_generated_questions)

                i += 1
                continue

        # 强制保存剩余的题目
        final_save_result = self.incremental_saver.force_save_all()
        if final_save_result.get('saved'):
            logger.info(f"最终保存: {final_save_result.get('questions_saved', 0)} 道题目")

        print()  # 进度条换行
        logger.info(f"文本块处理完成，成功生成 {len(all_questions)} 道题目，失败 {len(failed_chunks)} 个文本块")

        return all_questions, failed_chunks, successful_chunk_indices

    def _try_merge_chunks_for_generation(self, chunks: List[Dict[str, Any]], current_index: int) -> Dict[str, Any]:
        """
        尝试合并分块以生成足够的内容

        Args:
            chunks: 所有分块列表
            current_index: 当前分块索引

        Returns:
            合并结果字典，包含success, merged_chunk, next_index
        """
        if current_index >= len(chunks):
            return {'success': False, 'reason': 'index_out_of_range'}

        current_chunk = chunks[current_index]
        current_char_count = self.text_splitter.count_characters(current_chunk['content'])

        # 如果当前分块已经足够大，不进行合并
        if current_char_count >= self.config.MAX_CHUNK_SIZE:
            logger.warning(f"分块字符数 {current_char_count} 已达到最大分块大小 {self.config.MAX_CHUNK_SIZE}，无法合并")
            return {'success': False, 'reason': 'exceeds_max_chunk_size'}

        # 使用TextSplitter的合并方法
        merged_chunk, next_index = self.text_splitter.try_merge_with_next_chunks(chunks, current_index)

        if merged_chunk is None:
            return {'success': False, 'reason': 'no_chunks_to_merge'}

        # 检查合并后的字符数
        merged_char_count = merged_chunk.get('char_count', 0) if merged_chunk else 0

        logger.info(f"分块合并成功: 从索引 {current_index} 合并到 {next_index-1}, "
                   f"字符数从 {current_char_count} 增加到 {merged_char_count}")

        return {
            'success': True,
            'merged_chunk': merged_chunk,
            'next_index': next_index,
            'original_char_count': current_char_count,
            'merged_char_count': merged_char_count
        }

    def _save_results(self, questions: List[Dict[str, Any]], failed_chunks: List[Dict[str, Any]], successful_chunk_indices: List[int]) -> Dict[str, Any]:
        """保存处理结果（增量保存已处理主要保存工作）"""
        result_summary = summarize_results(questions, failed_chunks)

        try:
            # 获取增量保存的文件路径
            incremental_status = self.incremental_saver.get_status()
            if incremental_status['main_file_path']:
                result_summary['csv_file'] = incremental_status['main_file_path']
                logger.info(f"题库文件: {incremental_status['main_file_path']}")
                logger.info(f"总共保存: {incremental_status['total_saved_count']} 道题目")
            elif questions:
                # 如果增量保存没有文件，直接保存（备用方案）
                csv_file = self.csv_writer.save_questions_to_csv(questions)
                logger.info(f"备用保存 - 题库已保存到: {csv_file}")
                result_summary['csv_file'] = csv_file

            # 保存失败的文本块
            if failed_chunks:
                has_generated_questions = len(questions) > 0
                self.error_manager.save_failed_chunks_batch(failed_chunks, successful_chunk_indices=successful_chunk_indices, has_generated_questions=has_generated_questions)
                error_summary_file = self.error_manager.create_error_summary(failed_chunks)
                logger.info(f"错误摘要已保存到: {error_summary_file}")
                result_summary['error_summary_file'] = error_summary_file
            else:
                logger.info("没有失败的文本块，无需保存错误摘要。")
                result_summary['error_summary_file'] = "N/A"

            # 生成处理摘要报告
            summary_report = self.csv_writer.generate_summary_report(questions)
            summary_file = self.csv_writer.save_summary_report(summary_report)
            logger.info(f"处理摘要已保存到: {summary_file}")
            result_summary['summary_file'] = summary_file

            # 添加增量保存统计信息
            result_summary['incremental_save_info'] = {
                'total_saved': incremental_status['total_saved_count'],
                'backup_count': incremental_status['backup_counter'],
                'batch_threshold': incremental_status['batch_save_threshold'],
                'auto_backup_interval': incremental_status['auto_backup_interval']
            }

            # 添加成功生成题目的文本块索引
            result_summary['successful_chunk_indices'] = successful_chunk_indices

            return result_summary

        except Exception as e:
            logger.error(f"保存结果失败: {str(e)}")
            result_summary['save_error'] = str(e)
            return result_summary

    def test_api_connection(self) -> bool:
        """测试API连接"""
        logger.info("测试API连接...")
        return self.llm_client.test_connection() 