import tkinter as tk
from tkinter import ttk

class QuestionTypesConfigPanel(ttk.LabelFrame):
    def __init__(self, parent, *args, **kwargs):
        super().__init__(parent, *args, **kwargs)
        self.create_question_types_config_frame()

    def create_question_types_config_frame(self):
        """创建题型数量配置框架（在设置标签页中）"""
        types_frame = ttk.LabelFrame(self, text="题型数量配置", padding="5")
        types_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 题型配置说明 (移动到顶部右侧)
        info_text = "说明：设置每个文本块生成各种题型的数量，总题目数量为各题型数量之和。新出题逻辑将根据文档字数和基础字数自动调整题目数量。基础字数是指生成一道题所需的最少文本字数。基础题数是指在满足基础字数条件下，每个文本块或文档段落预期生成的题目数量。"
        ttk.Label(types_frame, text=info_text, font=("Arial", 8), foreground="gray", wraplength=300).grid(row=0, column=2, rowspan=7, sticky=tk.NW, padx=5, pady=5)

        # 单选题数量
        ttk.Label(types_frame, text="单选题数量:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.single_choice_var = tk.IntVar(value=4)
        single_frame = ttk.Frame(types_frame)
        single_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        single_scale = ttk.Scale(single_frame, from_=0, to=10, variable=self.single_choice_var,
                                orient=tk.HORIZONTAL, length=150)
        single_scale.pack(side=tk.LEFT)
        single_scale.configure(command=lambda v: self.single_choice_var.set(int(float(v))))
        ttk.Label(single_frame, textvariable=self.single_choice_var).pack(side=tk.LEFT, padx=5)

        # 多选题数量
        ttk.Label(types_frame, text="多选题数量:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.multiple_choice_var = tk.IntVar(value=2)
        multiple_frame = ttk.Frame(types_frame)
        multiple_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        multiple_scale = ttk.Scale(multiple_frame, from_=0, to=10, variable=self.multiple_choice_var,
                                  orient=tk.HORIZONTAL, length=150)
        multiple_scale.pack(side=tk.LEFT)
        multiple_scale.configure(command=lambda v: self.multiple_choice_var.set(int(float(v))))
        ttk.Label(multiple_frame, textvariable=self.multiple_choice_var).pack(side=tk.LEFT, padx=5)

        # 填空题数量
        ttk.Label(types_frame, text="填空题数量:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.fill_blank_var = tk.IntVar(value=2)
        fill_frame = ttk.Frame(types_frame)
        fill_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        fill_scale = ttk.Scale(fill_frame, from_=0, to=10, variable=self.fill_blank_var,
                              orient=tk.HORIZONTAL, length=150)
        fill_scale.pack(side=tk.LEFT)
        fill_scale.configure(command=lambda v: self.fill_blank_var.set(int(float(v))))
        ttk.Label(fill_frame, textvariable=self.fill_blank_var).pack(side=tk.LEFT, padx=5)

        # 简答题数量
        ttk.Label(types_frame, text="简答题数量:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.short_answer_var = tk.IntVar(value=1)
        short_frame = ttk.Frame(types_frame)
        short_frame.grid(row=4, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        short_scale = ttk.Scale(short_frame, from_=0, to=10, variable=self.short_answer_var,
                               orient=tk.HORIZONTAL, length=150)
        short_scale.pack(side=tk.LEFT)
        short_scale.configure(command=lambda v: self.short_answer_var.set(int(float(v))))
        ttk.Label(short_frame, textvariable=self.short_answer_var).pack(side=tk.LEFT, padx=5)

        # 判断题数量
        ttk.Label(types_frame, text="判断题数量:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.true_false_var = tk.IntVar(value=2)
        judge_frame = ttk.Frame(types_frame)
        judge_frame.grid(row=5, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        judge_scale = ttk.Scale(judge_frame, from_=0, to=10, variable=self.true_false_var,
                               orient=tk.HORIZONTAL, length=150)
        judge_scale.pack(side=tk.LEFT)
        judge_scale.configure(command=lambda v: self.true_false_var.set(int(float(v))))
        ttk.Label(judge_frame, textvariable=self.true_false_var).pack(side=tk.LEFT, padx=5)

        # 排序题数量
        ttk.Label(types_frame, text="排序题数量:").grid(row=6, column=0, sticky=tk.W, pady=5)
        self.sorting_var = tk.IntVar(value=0) # 默认为0，因为排序题比较复杂
        sorting_frame = ttk.Frame(types_frame)
        sorting_frame.grid(row=6, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        sorting_scale = ttk.Scale(sorting_frame, from_=0, to=10, variable=self.sorting_var,
                                 orient=tk.HORIZONTAL, length=150)
        sorting_scale.pack(side=tk.LEFT)
        sorting_scale.configure(command=lambda v: self.sorting_var.set(int(float(v))))
        ttk.Label(sorting_frame, textvariable=self.sorting_var).pack(side=tk.LEFT, padx=5)

        types_frame.columnconfigure(1, weight=1)